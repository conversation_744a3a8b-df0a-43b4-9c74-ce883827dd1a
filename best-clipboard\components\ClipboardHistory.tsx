import React from 'react';
import {
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Text, View } from '@/components/Themed';
import { ClipboardHistoryItem } from '@/types/clipboard';
import { useClipboardHistoryState } from '@/hooks/useClipboardHistoryState';

interface ClipboardHistoryProps {
  /** 最大显示高度 */
  maxHeight?: number;
  /** 是否显示清空按钮 */
  showClearButton?: boolean;
}

/**
 * 剪切板历史记录组件
 */
export function ClipboardHistory({ 
  maxHeight = 300, 
  showClearButton = true 
}: ClipboardHistoryProps) {
  const {
    items,
    loading,
    error,
    copyItem,
    removeItem,
    clearAll,
  } = useClipboardHistoryState();

  // 格式化时间显示
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 处理复制操作
  const handleCopy = async (item: ClipboardHistoryItem) => {
    try {
      await copyItem(item);
      Alert.alert('成功', '已复制到剪切板');
    } catch (error) {
      Alert.alert('错误', '复制失败');
    }
  };

  // 处理删除操作
  const handleDelete = (item: ClipboardHistoryItem) => {
    Alert.alert(
      '确认删除',
      '确定要删除这条记录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => removeItem(item.id),
        },
      ]
    );
  };

  // 处理清空所有记录
  const handleClearAll = () => {
    if (items.length === 0) return;
    
    Alert.alert(
      '确认清空',
      '确定要清空所有剪切板历史记录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '清空',
          style: 'destructive',
          onPress: clearAll,
        },
      ]
    );
  };

  // 获取内容类型图标
  const getTypeIcon = (type: ClipboardHistoryItem['type']) => {
    switch (type) {
      case 'url':
        return '🔗';
      case 'image':
        return '🖼️';
      default:
        return '📝';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>❌ {error}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>剪切板历史</Text>
        {showClearButton && items.length > 0 && (
          <TouchableOpacity onPress={handleClearAll} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>清空</Text>
          </TouchableOpacity>
        )}
      </View>

      {items.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>暂无剪切板历史记录</Text>
          <Text style={styles.emptyHint}>复制内容后会自动保存到这里</Text>
        </View>
      ) : (
        <ScrollView 
          style={[styles.scrollView, { maxHeight }]}
          showsVerticalScrollIndicator={true}
        >
          {items.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.historyItem}
              onPress={() => handleCopy(item)}
              onLongPress={() => handleDelete(item)}
              activeOpacity={0.7}
            >
              <View style={styles.itemHeader}>
                <Text style={styles.typeIcon}>{getTypeIcon(item.type)}</Text>
                <Text style={styles.timestamp}>{formatTime(item.timestamp)}</Text>
              </View>
              <Text style={styles.content} numberOfLines={3}>
                {item.preview}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#ff4444',
    borderRadius: 6,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  scrollView: {
    backgroundColor: 'transparent',
  },
  historyItem: {
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  typeIcon: {
    fontSize: 14,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
  },
  content: {
    fontSize: 14,
    lineHeight: 20,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  emptyHint: {
    fontSize: 12,
    color: '#999',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  errorText: {
    fontSize: 14,
    color: '#ff4444',
  },
});
