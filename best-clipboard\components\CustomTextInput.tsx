import React, { useRef, useEffect } from 'react';
import {
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TextInputProps,
} from 'react-native';
import { Text, View } from '@/components/Themed';
import { useCustomKeyboard } from '@/hooks/useCustomKeyboard';

interface CustomTextInputProps extends Omit<TextInputProps, 'onFocus'> {
  /** 输入框标签 */
  label?: string;
  /** 是否显示键盘切换按钮 */
  showKeyboardToggle?: boolean;
  /** 自定义样式 */
  containerStyle?: any;
  /** 输入框样式 */
  inputStyle?: any;
  /** 聚焦时的回调 */
  onFocus?: () => void;
}

/**
 * 支持自定义键盘的文本输入组件
 */
export function CustomTextInput({
  label,
  showKeyboardToggle = true,
  containerStyle,
  inputStyle,
  onFocus,
  ...textInputProps
}: CustomTextInputProps) {
  const inputRef = useRef<TextInput>(null);
  const {
    useCustomKeyboard,
    showCustomKeyboard,
    showSystemKeyboard,
    setCurrentInputRef,
    toggleKeyboardMode,
  } = useCustomKeyboard();

  // 设置输入框引用
  useEffect(() => {
    setCurrentInputRef(inputRef);
  }, [setCurrentInputRef]);

  // 处理输入框聚焦
  const handleFocus = () => {
    if (useCustomKeyboard) {
      // 如果使用自定义键盘，阻止系统键盘显示
      inputRef.current?.blur();
      showCustomKeyboard(inputRef);
    } else {
      // 使用系统键盘
      showSystemKeyboard();
    }
    
    onFocus?.();
  };

  // 处理键盘模式切换
  const handleToggleKeyboard = () => {
    toggleKeyboardMode();
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <View style={styles.inputContainer}>
        <TextInput
          ref={inputRef}
          style={[styles.input, inputStyle]}
          onFocus={handleFocus}
          {...textInputProps}
        />
        
        {showKeyboardToggle && (
          <TouchableOpacity
            style={styles.toggleButton}
            onPress={handleToggleKeyboard}
          >
            <Text style={styles.toggleButtonText}>
              {useCustomKeyboard ? '🔤' : '⌨️'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
      
      {showKeyboardToggle && (
        <Text style={styles.hint}>
          点击 {useCustomKeyboard ? '🔤' : '⌨️'} 切换到{useCustomKeyboard ? '系统' : '自定义'}键盘
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  input: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  toggleButton: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderLeftWidth: 1,
    borderLeftColor: '#ddd',
    backgroundColor: '#f8f8f8',
  },
  toggleButtonText: {
    fontSize: 18,
  },
  hint: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'right',
  },
});
