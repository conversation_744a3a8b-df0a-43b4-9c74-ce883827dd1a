import React, { useState } from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Alert,
} from 'react-native';
import { Text, View } from '@/components/Themed';
import { ClipboardHistory } from '@/components/ClipboardHistory';
import { useClipboardHistoryState } from '@/hooks/useClipboardHistoryState';

const { width: screenWidth } = Dimensions.get('window');

interface CustomKeyboardProps {
  /** 当前输入的文本 */
  value: string;
  /** 文本变化回调 */
  onChangeText: (text: string) => void;
  /** 键盘高度 */
  height?: number;
  /** 是否显示键盘 */
  visible: boolean;
  /** 隐藏键盘回调 */
  onHide: () => void;
}

/**
 * 自定义键盘组件，集成剪切板历史功能
 */
export function CustomKeyboard({
  value,
  onChangeText,
  height = 300,
  visible,
  onHide,
}: CustomKeyboardProps) {
  const [activeTab, setActiveTab] = useState<'keyboard' | 'clipboard'>('keyboard');
  const { items, copyItem } = useClipboardHistoryState();

  // 键盘按键布局
  const keyboardLayout = [
    ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm'],
  ];

  // 处理按键点击
  const handleKeyPress = (key: string) => {
    if (key === 'backspace') {
      onChangeText(value.slice(0, -1));
    } else if (key === 'space') {
      onChangeText(value + ' ');
    } else if (key === 'enter') {
      onChangeText(value + '\n');
    } else {
      onChangeText(value + key);
    }
  };

  // 处理剪切板项目选择
  const handleClipboardItemSelect = async (item: any) => {
    try {
      onChangeText(value + item.content);
      await copyItem(item);
      Alert.alert('成功', '已插入文本');
    } catch (error) {
      Alert.alert('错误', '插入文本失败');
    }
  };

  // 清空输入
  const handleClear = () => {
    onChangeText('');
  };

  if (!visible) {
    return null;
  }

  return (
    <View style={[styles.container, { height }]}>
      {/* 键盘头部 */}
      <View style={styles.header}>
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'keyboard' && styles.activeTab]}
            onPress={() => setActiveTab('keyboard')}
          >
            <Text style={[styles.tabText, activeTab === 'keyboard' && styles.activeTabText]}>
              ⌨️ 键盘
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'clipboard' && styles.activeTab]}
            onPress={() => setActiveTab('clipboard')}
          >
            <Text style={[styles.tabText, activeTab === 'clipboard' && styles.activeTabText]}>
              📋 剪切板
            </Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity style={styles.hideButton} onPress={onHide}>
          <Text style={styles.hideButtonText}>隐藏</Text>
        </TouchableOpacity>
      </View>

      {/* 键盘内容区域 */}
      <View style={styles.content}>
        {activeTab === 'keyboard' ? (
          <View style={styles.keyboardContainer}>
            {/* 功能按钮行 */}
            <View style={styles.functionRow}>
              <TouchableOpacity style={styles.functionButton} onPress={handleClear}>
                <Text style={styles.functionButtonText}>清空</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.functionButton} 
                onPress={() => handleKeyPress('space')}
              >
                <Text style={styles.functionButtonText}>空格</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.functionButton} 
                onPress={() => handleKeyPress('enter')}
              >
                <Text style={styles.functionButtonText}>换行</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.functionButton} 
                onPress={() => handleKeyPress('backspace')}
              >
                <Text style={styles.functionButtonText}>⌫</Text>
              </TouchableOpacity>
            </View>

            {/* 字母数字键盘 */}
            <ScrollView style={styles.keyboardScrollView}>
              {keyboardLayout.map((row, rowIndex) => (
                <View key={rowIndex} style={styles.keyRow}>
                  {row.map((key) => (
                    <TouchableOpacity
                      key={key}
                      style={styles.key}
                      onPress={() => handleKeyPress(key)}
                    >
                      <Text style={styles.keyText}>{key}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              ))}
            </ScrollView>
          </View>
        ) : (
          <View style={styles.clipboardContainer}>
            {items.length === 0 ? (
              <View style={styles.emptyClipboard}>
                <Text style={styles.emptyText}>暂无剪切板历史</Text>
                <Text style={styles.emptyHint}>复制内容后会显示在这里</Text>
              </View>
            ) : (
              <ScrollView style={styles.clipboardScrollView}>
                {items.map((item) => (
                  <TouchableOpacity
                    key={item.id}
                    style={styles.clipboardItem}
                    onPress={() => handleClipboardItemSelect(item)}
                  >
                    <Text style={styles.clipboardPreview} numberOfLines={2}>
                      {item.preview}
                    </Text>
                    <Text style={styles.clipboardTime}>
                      {new Date(item.timestamp).toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            )}
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    borderTopWidth: 1,
    borderTopColor: '#ccc',
    width: screenWidth,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    backgroundColor: '#e8e8e8',
  },
  tabContainer: {
    flexDirection: 'row',
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 6,
    backgroundColor: 'transparent',
  },
  activeTab: {
    backgroundColor: '#2f95dc',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '500',
  },
  hideButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#666',
    borderRadius: 6,
  },
  hideButtonText: {
    color: '#fff',
    fontSize: 12,
  },
  content: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
    padding: 8,
  },
  functionRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  functionButton: {
    backgroundColor: '#ddd',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 60,
    alignItems: 'center',
  },
  functionButtonText: {
    fontSize: 12,
    color: '#333',
  },
  keyboardScrollView: {
    flex: 1,
  },
  keyRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 4,
  },
  key: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginHorizontal: 2,
    minWidth: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  keyText: {
    fontSize: 16,
    color: '#333',
  },
  clipboardContainer: {
    flex: 1,
    padding: 8,
  },
  emptyClipboard: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  emptyHint: {
    fontSize: 12,
    color: '#999',
  },
  clipboardScrollView: {
    flex: 1,
  },
  clipboardItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  clipboardPreview: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  clipboardTime: {
    fontSize: 10,
    color: '#999',
    textAlign: 'right',
  },
});
