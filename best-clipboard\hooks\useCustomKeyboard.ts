import { useState, useCallback, useRef } from 'react';
import { Keyboard, TextInput } from 'react-native';

export interface CustomKeyboardState {
  /** 是否显示自定义键盘 */
  isCustomKeyboardVisible: boolean;
  /** 是否使用自定义键盘模式 */
  useCustomKeyboard: boolean;
  /** 当前输入框的引用 */
  currentInputRef: React.RefObject<TextInput> | null;
}

export interface CustomKeyboardActions {
  /** 显示自定义键盘 */
  showCustomKeyboard: (inputRef?: React.RefObject<TextInput>) => void;
  /** 隐藏自定义键盘 */
  hideCustomKeyboard: () => void;
  /** 切换键盘模式 */
  toggleKeyboardMode: () => void;
  /** 设置当前输入框引用 */
  setCurrentInputRef: (ref: React.RefObject<TextInput>) => void;
  /** 显示系统键盘 */
  showSystemKeyboard: () => void;
  /** 隐藏系统键盘 */
  hideSystemKeyboard: () => void;
}

/**
 * 自定义键盘状态管理Hook
 */
export function useCustomKeyboard() {
  const [state, setState] = useState<CustomKeyboardState>({
    isCustomKeyboardVisible: false,
    useCustomKeyboard: false,
    currentInputRef: null,
  });

  // 显示自定义键盘
  const showCustomKeyboard = useCallback((inputRef?: React.RefObject<TextInput>) => {
    // 先隐藏系统键盘
    Keyboard.dismiss();
    
    setState(prev => ({
      ...prev,
      isCustomKeyboardVisible: true,
      useCustomKeyboard: true,
      currentInputRef: inputRef || prev.currentInputRef,
    }));
  }, []);

  // 隐藏自定义键盘
  const hideCustomKeyboard = useCallback(() => {
    setState(prev => ({
      ...prev,
      isCustomKeyboardVisible: false,
    }));
  }, []);

  // 切换键盘模式
  const toggleKeyboardMode = useCallback(() => {
    setState(prev => {
      const newUseCustomKeyboard = !prev.useCustomKeyboard;
      
      if (newUseCustomKeyboard) {
        // 切换到自定义键盘
        Keyboard.dismiss();
        return {
          ...prev,
          useCustomKeyboard: true,
          isCustomKeyboardVisible: true,
        };
      } else {
        // 切换到系统键盘
        return {
          ...prev,
          useCustomKeyboard: false,
          isCustomKeyboardVisible: false,
        };
      }
    });
  }, []);

  // 设置当前输入框引用
  const setCurrentInputRef = useCallback((ref: React.RefObject<TextInput>) => {
    setState(prev => ({
      ...prev,
      currentInputRef: ref,
    }));
  }, []);

  // 显示系统键盘
  const showSystemKeyboard = useCallback(() => {
    setState(prev => ({
      ...prev,
      useCustomKeyboard: false,
      isCustomKeyboardVisible: false,
    }));
    
    // 聚焦当前输入框以显示系统键盘
    if (state.currentInputRef?.current) {
      state.currentInputRef.current.focus();
    }
  }, [state.currentInputRef]);

  // 隐藏系统键盘
  const hideSystemKeyboard = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  const actions: CustomKeyboardActions = {
    showCustomKeyboard,
    hideCustomKeyboard,
    toggleKeyboardMode,
    setCurrentInputRef,
    showSystemKeyboard,
    hideSystemKeyboard,
  };

  return {
    ...state,
    ...actions,
  };
}
