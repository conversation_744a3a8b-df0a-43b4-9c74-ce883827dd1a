import { StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Text, View } from '@/components/Themed';
import { ClipboardHistory } from '@/components/ClipboardHistory';
import { useClipboardHistoryState } from '@/hooks/useClipboardHistoryState';
import * as Clipboard from 'expo-clipboard';

export default function SettingsScreen() {
  const { addItem } = useClipboardHistoryState();

  // 添加测试剪切板内容的功能
  const handleAddTestContent = async () => {
    const testContents = [
      'Hello World! 这是一个测试文本',
      'https://www.example.com',
      '这是一段比较长的文本内容，用来测试剪切板历史记录的预览功能是否正常工作。这段文本应该会被截断显示。',
      '复制这段文本到剪切板',
      'React Native + Expo 开发的剪切板应用'
    ];

    const randomContent = testContents[Math.floor(Math.random() * testContents.length)];
    await addItem(randomContent);
    Alert.alert('成功', `已添加测试内容: ${randomContent.substring(0, 20)}...`);
  };

  const handleGetClipboard = async () => {
    try {
      const content = await Clipboard.getStringAsync();
      if (content.trim()) {
        await addItem(content);
        Alert.alert('成功', '已保存当前剪切板内容');
      } else {
        Alert.alert('提示', '剪切板为空');
      }
    } catch (error) {
      Alert.alert('错误', '获取剪切板内容失败');
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Text style={styles.title}>设置</Text>
      <View style={styles.separator} lightColor="#eee" darkColor="rgba(255,255,255,0.1)" />

      {/* 应用信息区域 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>应用信息</Text>
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>版本信息</Text>
          <Text style={styles.settingValue}>v1.0.0</Text>
        </View>
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>开发框架</Text>
          <Text style={styles.settingValue}>React Native + Expo</Text>
        </View>
      </View>

      {/* 剪切板操作区域 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>剪切板操作</Text>
        <TouchableOpacity style={styles.actionButton} onPress={handleGetClipboard}>
          <Text style={styles.actionButtonText}>保存当前剪切板内容</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={handleAddTestContent}>
          <Text style={styles.actionButtonText}>添加测试内容</Text>
        </TouchableOpacity>
      </View>

      {/* 剪切板历史记录区域 */}
      <View style={styles.section}>
        <ClipboardHistory maxHeight={400} showClearButton={true} />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  separator: {
    marginVertical: 20,
    height: 1,
    width: '80%',
    alignSelf: 'center',
  },
  section: {
    marginBottom: 30,
    width: '100%',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 4,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingValue: {
    fontSize: 14,
    color: '#666',
  },
  actionButton: {
    backgroundColor: '#2f95dc',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginVertical: 4,
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
