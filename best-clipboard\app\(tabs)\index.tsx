import { StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, View } from '@/components/Themed';
import { CustomTextInput } from '@/components/CustomTextInput';
import { CustomKeyboard } from '@/components/CustomKeyboard';
import { useCustomKeyboard } from '@/hooks/useCustomKeyboard';
import { useState } from 'react';

export default function HomeScreen() {
  const [text1, setText1] = useState('');
  const [text2, setText2] = useState('');
  const [multilineText, setMultilineText] = useState('');

  const {
    isCustomKeyboardVisible,
    useCustomKeyboard: isUsingCustomKeyboard,
    hideCustomKeyboard,
  } = useCustomKeyboard();

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        keyboardShouldPersistTaps="handled"
      >
        <Text style={styles.title}>Best Clipboard</Text>
        <Text style={styles.subtitle}>智能剪切板键盘演示</Text>
        <View style={styles.separator} lightColor="#eee" darkColor="rgba(255,255,255,0.1)" />

        <Text style={styles.description}>
          体验集成剪切板历史的自定义键盘功能
        </Text>

        {/* 输入演示区域 */}
        <View style={styles.inputSection}>
          <CustomTextInput
            label="单行文本输入"
            placeholder="点击输入文本..."
            value={text1}
            onChangeText={setText1}
            showKeyboardToggle={true}
          />

          <CustomTextInput
            label="邮箱输入"
            placeholder="输入邮箱地址..."
            value={text2}
            onChangeText={setText2}
            keyboardType="email-address"
            showKeyboardToggle={true}
          />

          <CustomTextInput
            label="多行文本输入"
            placeholder="输入多行文本..."
            value={multilineText}
            onChangeText={setMultilineText}
            multiline={true}
            numberOfLines={4}
            showKeyboardToggle={true}
            inputStyle={styles.multilineInput}
          />
        </View>

        <Text style={styles.features}>
          ✅ 自定义键盘界面{'\n'}
          ✅ 剪切板历史集成{'\n'}
          ✅ 键盘模式切换{'\n'}
          ✅ 智能文本输入
        </Text>
      </ScrollView>

      {/* 自定义键盘 */}
      <CustomKeyboard
        visible={isCustomKeyboardVisible}
        value={text1} // 这里可以根据当前聚焦的输入框动态切换
        onChangeText={setText1}
        onHide={hideCustomKeyboard}
        height={300}
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  separator: {
    marginVertical: 20,
    height: 1,
    width: '80%',
    alignSelf: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
    color: '#333',
  },
  inputSection: {
    width: '100%',
    marginBottom: 30,
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  features: {
    fontSize: 14,
    textAlign: 'left',
    lineHeight: 22,
    backgroundColor: 'rgba(0,0,0,0.05)',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
  },
});
