import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Clipboard from 'expo-clipboard';
import { 
  ClipboardHistoryItem, 
  ClipboardHistoryState, 
  ClipboardHistoryActions,
  ClipboardHistoryConfig 
} from '@/types/clipboard';

const STORAGE_KEY = 'clipboard_history';
const DEFAULT_CONFIG: ClipboardHistoryConfig = {
  maxItems: 50,
  autoSave: true,
  previewLength: 100,
};

/**
 * 剪切板历史记录状态管理Hook
 */
export function useClipboardHistoryState(config: Partial<ClipboardHistoryConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  const [state, setState] = useState<ClipboardHistoryState>({
    items: [],
    loading: true,
    error: null,
  });

  // 生成唯一ID
  const generateId = useCallback(() => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }, []);

  // 创建内容预览
  const createPreview = useCallback((content: string) => {
    return content.length > finalConfig.previewLength 
      ? content.substring(0, finalConfig.previewLength) + '...'
      : content;
  }, [finalConfig.previewLength]);

  // 检测内容类型
  const detectContentType = useCallback((content: string): ClipboardHistoryItem['type'] => {
    // 简单的URL检测
    const urlRegex = /^https?:\/\/.+/i;
    if (urlRegex.test(content.trim())) {
      return 'url';
    }
    // 默认为文本类型
    return 'text';
  }, []);

  // 从存储加载历史记录
  const loadHistory = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      if (stored) {
        const items: ClipboardHistoryItem[] = JSON.parse(stored);
        setState(prev => ({ ...prev, items, loading: false }));
      } else {
        setState(prev => ({ ...prev, items: [], loading: false }));
      }
    } catch (error) {
      console.error('加载剪切板历史失败:', error);
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: '加载历史记录失败' 
      }));
    }
  }, []);

  // 保存历史记录到存储
  const saveHistory = useCallback(async (items: ClipboardHistoryItem[]) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(items));
    } catch (error) {
      console.error('保存剪切板历史失败:', error);
      setState(prev => ({ ...prev, error: '保存历史记录失败' }));
    }
  }, []);

  // 添加新记录
  const addItem = useCallback(async (content: string) => {
    if (!content.trim()) return;

    try {
      const newItem: ClipboardHistoryItem = {
        id: generateId(),
        content: content.trim(),
        timestamp: Date.now(),
        type: detectContentType(content),
        preview: createPreview(content.trim()),
      };

      setState(prev => {
        // 检查是否已存在相同内容
        const existingIndex = prev.items.findIndex(item => item.content === newItem.content);
        let newItems: ClipboardHistoryItem[];
        
        if (existingIndex >= 0) {
          // 如果存在，移除旧的并添加到顶部
          newItems = [newItem, ...prev.items.filter((_, index) => index !== existingIndex)];
        } else {
          // 添加到顶部
          newItems = [newItem, ...prev.items];
        }

        // 限制最大数量
        if (newItems.length > finalConfig.maxItems) {
          newItems = newItems.slice(0, finalConfig.maxItems);
        }

        // 保存到存储
        saveHistory(newItems);

        return { ...prev, items: newItems, error: null };
      });
    } catch (error) {
      console.error('添加剪切板记录失败:', error);
      setState(prev => ({ ...prev, error: '添加记录失败' }));
    }
  }, [generateId, detectContentType, createPreview, finalConfig.maxItems, saveHistory]);

  // 删除记录
  const removeItem = useCallback(async (id: string) => {
    try {
      setState(prev => {
        const newItems = prev.items.filter(item => item.id !== id);
        saveHistory(newItems);
        return { ...prev, items: newItems, error: null };
      });
    } catch (error) {
      console.error('删除剪切板记录失败:', error);
      setState(prev => ({ ...prev, error: '删除记录失败' }));
    }
  }, [saveHistory]);

  // 清空所有记录
  const clearAll = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, items: [], error: null }));
      await AsyncStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('清空剪切板历史失败:', error);
      setState(prev => ({ ...prev, error: '清空历史记录失败' }));
    }
  }, []);

  // 复制记录到剪切板
  const copyItem = useCallback(async (item: ClipboardHistoryItem) => {
    try {
      await Clipboard.setStringAsync(item.content);
      // 将该记录移到顶部
      setState(prev => {
        const newItems = [item, ...prev.items.filter(i => i.id !== item.id)];
        saveHistory(newItems);
        return { ...prev, items: newItems, error: null };
      });
    } catch (error) {
      console.error('复制到剪切板失败:', error);
      setState(prev => ({ ...prev, error: '复制失败' }));
    }
  }, [saveHistory]);

  // 刷新历史记录
  const refresh = useCallback(async () => {
    await loadHistory();
  }, [loadHistory]);

  // 组件挂载时加载历史记录
  useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  const actions: ClipboardHistoryActions = {
    addItem,
    removeItem,
    clearAll,
    copyItem,
    refresh,
  };

  return {
    ...state,
    ...actions,
  };
}
